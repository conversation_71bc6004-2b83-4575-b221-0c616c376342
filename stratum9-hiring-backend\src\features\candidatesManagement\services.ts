import { InvokeCommand, LambdaClient } from "@aws-sdk/client-lambda";
import { In } from "typeorm";
import * as Sentry from "@sentry/node";
import dbConnection from "../../db/dbConnection";
import CandidatesModel from "../../schema/s9-innerview/candidates";
import { ResponseObject } from "../../interface/commonInterface";
import JobApplicationsModel, {
  ApplicationRankStatus,
  Status,
} from "../../schema/s9-innerview/job_applications";
import {
  CANDIDATE_APPLICATION_MSG,
  DEFAULT_LIMIT,
} from "../../utils/constants";
import AuthServices from "../auth/services";
import ApplicantAdditionalInfoModel from "../../schema/s9-innerview/applicant_additional_info";
import { ApplicantAdditionalInfo } from "../resumeScreen/interface";
import InterviewModel from "../../schema/s9-innerview/interview";
import UserModel from "../../schema/s9/user";
import {
  FlattenedSkillScore,
  // GroupedSkillScore,
  SkillEvaluationRaw,
} from "./interface";
import NotificationServices from "../notification/services";
import { NotificationType } from "../../schema/s9-innerview/notifications";
import Role from "../../schema/s9-innerview/roles";
import FinalAssessmentsModel from "../../schema/s9-innerview/final_assessments";
// eslint-disable-next-line import/prefer-default-export
import { clientConfig } from "../../config/awsConfig";
// import envConfig from "../../config/envConfig";
// const CONFIG = envConfig();
export class CandidateApplicationService {
  static async getAllCandidates(
    orgId: number,
    jobId: number,
    isActive?: boolean,
    searchStr: string = "",
    offset: number = 0,
    limit: number = DEFAULT_LIMIT
  ): Promise<ResponseObject> {
    try {
      console.log("isActive>>>>>>>>>", isActive);
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const candidatesRepo = dataSource.getRepository(CandidatesModel);

      const query = candidatesRepo
        .createQueryBuilder("candidate")
        .innerJoin(
          "job_applications",
          "jobApplication",
          "jobApplication.candidate_id = candidate.id"
        )
        .where("candidate.orgId = :orgId", { orgId })
        .andWhere("jobApplication.isActive = :isActive", { isActive });

      console.log("query>>>>>>>>>", query);
      if (jobId) {
        query.andWhere("jobApplication.jobId = :jobId", { jobId });
        console.log("jobId>>>>>>>>>", jobId);
      }
      console.log("query data after jobid>>>>>>>>>", query);

      if (searchStr.trim().length > 0) {
        query.andWhere("LOWER(candidate.name) LIKE LOWER(:searchStr)", {
          searchStr: `%${searchStr.trim()}%`,
        });
      }

      const data = await query
        .andWhere("jobApplication.isTopApplication = false")
        .orderBy(
          "CAST(JSON_UNQUOTE(JSON_EXTRACT(jobApplication.ats_score, '$.total_ats_score')) AS DECIMAL)",
          "DESC"
        )
        .offset(offset)
        .limit(limit)
        .select([
          "candidate.id AS candidateId",
          "candidate.name AS candidateName",
          "jobApplication.id AS applicationId",
          "jobApplication.status AS applicationStatus",
          "jobApplication.source AS applicationSource",
          "jobApplication.created_ts AS applicationCreatedTs",
          "jobApplication.updated_ts AS applicationUpdatedTs",
          "jobApplication.isActive AS isActive",
          "jobApplication.jobId AS jobId",
          "jobApplication.hiringManagerId AS hiringManagerId",
          "jobApplication.hiringManagerReason AS hiringManagerReason",
          "jobApplication.applicationRankStatus AS applicationRankStatus",
          "jobApplication.aiReason AS aiReason",
          "jobApplication.aiDecision AS aiDecision",
          "JSON_UNQUOTE(JSON_EXTRACT(jobApplication.atsScore, '$.total_ats_score')) AS atsScore",
        ])
        .getRawMany();

      console.log("data>>>>>>>>>", data);

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.candidates_fetched,
        data,
      };
    } catch (error: any) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_all_candidates_failed,
        error: error.message,
      };
    }
  }

  static async archiveActiveApplication(
    applicationId: number,
    orgId: number,
    status: boolean,
    reason?: string
  ): Promise<ResponseObject> {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobAppRepo = dataSource.getRepository(JobApplicationsModel);

      const jobApplication = await jobAppRepo
        .createQueryBuilder("jobApplication")
        .innerJoinAndSelect("jobApplication.candidate", "candidate")
        .where("jobApplication.id = :applicationId", { applicationId })
        .andWhere("candidate.orgId = :orgId", { orgId })
        .getOne();

      if (!jobApplication) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.job_application_not_found,
        };
      }

      jobApplication.isActive = status;
      jobApplication.isTopApplication = false; // Reset top application status when archiving
      jobApplication.applicationRankStatus = ApplicationRankStatus.NO_CHANGES;
      jobApplication.updatedTs = new Date();

      if (status === false && typeof reason === "string") {
        jobApplication.hiringManagerReason = reason.trim();
      } else if (status === true) {
        jobApplication.hiringManagerReason = null;
      }

      await jobAppRepo.save(jobApplication);

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_success,
      };
    } catch (error: any) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_failed,
        error: error.message,
      };
    }
  }

  // get top candidates

  static getTopCandidates = async (orgId: number, jobId: number) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobApplicationRepo = dataSource.getRepository(JobApplicationsModel);

      const query = jobApplicationRepo
        .createQueryBuilder("jobApplication")
        .innerJoin("jobApplication.candidate", "candidate")
        .where("candidate.orgId = :orgId", { orgId })
        .andWhere("jobApplication.jobId = :jobId", { jobId })
        .andWhere("jobApplication.isActive = true")
        .andWhere("jobApplication.isTopApplication = true")
        .orderBy(
          "CAST(JSON_UNQUOTE(JSON_EXTRACT(jobApplication.ats_score, '$.total_ats_score')) AS DECIMAL)",
          "DESC"
        )
        .select([
          "candidate.name AS candidateName",
          "jobApplication.id AS applicationId",
          "jobApplication.applicationRankStatus AS applicationRankStatus",
          "candidate.id AS candidateId",
          "jobApplication.status AS applicationStatus",
          "jobApplication.source AS applicationSource",
          "jobApplication.created_ts AS applicationCreatedTs",
          "jobApplication.updated_ts AS applicationUpdatedTs",
          "jobApplication.hiring_manager_reason AS hiringManagerReason",
          "jobApplication.ai_reason AS aiReason",
          "jobApplication.ai_decision AS aiDecision",
          "jobApplication.job_id AS job_id",
          "jobApplication.isTopApplication AS isTopApplication",
          "JSON_UNQUOTE(JSON_EXTRACT(jobApplication.ats_score, '$.total_ats_score')) AS atsScore",
        ]);

      const data = await query.getRawMany();

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.top_candidates_retrieved,
        data,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_top_candidates_failed,
        error: error.message,
      };
    }
  };

  // promote or demote candidates

  static promoteDemoteCandidate = async (
    candidateId: number,
    applicationId: number,
    orgId: number,
    action: ApplicationRankStatus.PROMOTED | ApplicationRankStatus.DEMOTED
  ) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobAppRepo = dataSource.getRepository(JobApplicationsModel);

      const application = await jobAppRepo
        .createQueryBuilder("jobApplication")
        .innerJoin("jobApplication.candidate", "candidate")
        .where("jobApplication.id = :applicationId", { applicationId })
        .andWhere("jobApplication.candidateId = :candidateId", { candidateId })
        .andWhere("candidate.orgId = :orgId", { orgId })
        .andWhere("jobApplication.isActive = true")
        .getOne();

      if (!application) {
        throw new Error(
          CANDIDATE_APPLICATION_MSG.candidate_application_not_found
        );
      }

      const isTopApplication = action === ApplicationRankStatus.PROMOTED;
      const applicationRankStatus = isTopApplication
        ? ApplicationRankStatus.PROMOTED
        : ApplicationRankStatus.DEMOTED;

      application.isTopApplication = isTopApplication;
      application.applicationRankStatus = applicationRankStatus;

      await jobAppRepo.save(application);
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.update_rank_status_success,
      };
    } catch (error: any) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.update_rank_status_failed,
        error: error.message,
      };
    }
  };

  static getCandidateDetails = async (
    jobApplicationId: number,
    orgId: number
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const interviewRepo = dataSource.getRepository(InterviewModel);

      // make below query first from job_application table
      const query = dataSource
        .getRepository(JobApplicationsModel)
        .createQueryBuilder("jobApplication")
        .leftJoinAndSelect(
          "candidates",
          "candidate",
          "jobApplication.candidateId = candidate.id"
        )
        .innerJoinAndSelect(
          "jobApplication.job",
          "job",
          "job.id = jobApplication.jobId"
        )
        .leftJoinAndSelect(
          "job.department",
          "department",
          "department.id = job.departmentId"
        )
        .where("jobApplication.id = :jobApplicationId", { jobApplicationId })
        .andWhere("job.orgId = :orgId", { orgId })
        .andWhere("jobApplication.isActive = true")
        .select([
          "candidate.name as candidateName",
          "jobApplication.id as jobApplicationId",
          "jobApplication.jobId as jobId",
          "job.title as jobTitle",
          "jobApplication.status as status",
          "jobApplication.resume_file as resumeLink",
          "jobApplication.hiring_manager_id as hiringManagerId",
          "candidate.imageUrl AS imageUrl",
          "department.name AS department",
          "jobApplication.isActive AS isActive",
        ]);

      console.log("query>>>>>>>>>", query);
      // const query = dataSource
      //   .getRepository(CandidatesModel)
      //   .createQueryBuilder("candidate")
      //   .leftJoinAndSelect(
      //     "job_applications",
      //     "jobApplication",
      //     "jobApplication.candidateId = candidate.id"
      //   )
      //   .leftJoinAndSelect(
      //     "jobApplication.job",
      //     "job",
      //     "job.id = jobApplication.jobId"
      //   )

      //   .leftJoinAndSelect(
      //     "job.department",
      //     "department",
      //     "department.id = job.departmentId"
      //   )
      //   // .leftJoinAndSelect(
      //   //   "jobApplication.statusHistory",
      //   //   "statusHistory"
      //   // )
      //   .where("jobApplication.id = :jobApplicationId", { jobApplicationId })
      //   .andWhere("candidate.orgId = :orgId", { orgId })
      //   .select([
      //     "candidate.name as candidateName",
      //     "jobApplication.id as jobApplicationId",
      //     "jobApplication.jobId as jobId",
      //     "job.title as jobTitle",
      //     "jobApplication.status as status",
      //     "jobApplication.resume_file as resumeLink",
      //     "jobApplication.hiring_manager_id as hiringManagerId",
      //     "candidate.imageUrl AS imageUrl",
      //     "department.name AS department",
      //   ]);
      // .setParameter('clearedStatus', Status.APPROVED);

      const result = await query.getRawOne();

      if (!result) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.candidate_not_found,
        };
      }

      const interviewInfo = await interviewRepo.findOne({
        where: {
          jobApplicationId: result.jobApplicationId,
        },
        order: {
          id: "DESC",
        },
      });
      const interviewerInfo = await AuthServices.getUserByUserId(
        result.hiringManagerId
      );
      const isFinalAssessmentExists = await dataSource
        .getRepository(FinalAssessmentsModel)
        .findOne({
          where: {
            jobApplicationId: result.jobApplicationId,
          },
        });
      console.log(
        "isFinalAssessmentExists>>>>>>>>>>>>",
        isFinalAssessmentExists
      );

      console.log(">>>>>>>>>>>", result);

      return {
        success: true,
        data: {
          ...result,
          interviewerName: `${interviewerInfo?.first_name} ${interviewerInfo?.last_name}`,
          interviewerImage: interviewerInfo?.image,
          roundNumber: interviewInfo?.roundNumber,
          // eslint-disable-next-line no-unneeded-ternary
          isFinalAssessmentExists: isFinalAssessmentExists ? true : false,
        },
      };
    } catch (error: unknown) {
      console.log("Error in getCandidateDetails:", error);

      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.fetch_candidate_details_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  static addApplicantAdditionalInfo = async (
    orgId: number,
    body: ApplicantAdditionalInfo
  ) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();

      const jobApplication = await dataSource
        .getRepository(JobApplicationsModel)
        .createQueryBuilder("jobApplication")
        .innerJoin("jobApplication.candidate", "candidate")
        .where("jobApplication.id = :applicationId", {
          applicationId: body.applicationId,
        })
        .andWhere("candidate.orgId = :orgId", { orgId })
        .andWhere("jobApplication.isActive = true")
        .getOne();

      if (!jobApplication) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.job_application_not_found,
        };
      }
      // Step 1: Check if candidate exists for given orgId and candidateId
      const candidate = await dataSource
        .getRepository(CandidatesModel)
        .findOne({
          where: {
            id: jobApplication.candidateId,
            orgId,
          },
        });

      if (!candidate) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.candidate_not_found_for_org,
        };
      }

      // Step 2: Save additional info
      const repo = dataSource.getRepository(ApplicantAdditionalInfoModel);

      const newInfo = new ApplicantAdditionalInfoModel();
      newInfo.description = body.description;
      newInfo.jobApplicationId = body.applicationId;
      newInfo.images = body.images ? { urls: [body.images] } : null;

      const savedInfo = await repo.save(newInfo);
      console.log("savedInfo", savedInfo);
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.additional_info_saved,
        data: savedInfo,
      };
    } catch (error: unknown) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.save_additional_info_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  static updateJobApplicationStatus = async (
    jobApplicationId: number,
    status: string,
    orgId: number
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobApplicationRepo = dataSource.getRepository(JobApplicationsModel);
      const finalAssessmentRepo = dataSource.getRepository(
        FinalAssessmentsModel
      );

      // Check if job application exists
      const jobApplication = await jobApplicationRepo
        .createQueryBuilder("jobApplication")
        .innerJoinAndSelect("jobApplication.job", "job")
        .where("jobApplication.id = :jobApplicationId", { jobApplicationId })
        .andWhere("jobApplication.isActive = true")
        .andWhere("job.orgId = :orgId", { orgId })
        .andWhere("jobApplication.isActive = true")
        .getOne();

      console.log("jobApplication>>>>>>>>>>>>", jobApplication);

      if (!jobApplication) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.candidate_application_not_found,
        };
      }

      const finalAssessment = await finalAssessmentRepo.findOne({
        where: {
          jobApplicationId,
        },
      });

      if (!finalAssessment) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.final_assessment_not_exists,
        };
      }

      console.log("satatus>>>>>>>>>>>>", status);

      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const interviews = await interviewRepo
        .createQueryBuilder("interview")
        .select([
          `DISTINCT interview.scheduledBy as scheduledBy`,
          "interview.isEnded as isEnded",
          "interview.isFeedbackFilled as isFeedbackFilled",
        ])
        .where("interview.jobApplicationId = :jobApplicationId", {
          jobApplicationId,
        })
        .andWhere("interview.is_active = true")

        .getRawMany();

      console.log("interviews>>>>>>>>>>>>", interviews);
      if (!interviews || !interviews.length) {
        return {
          success: false,
          message:
            CANDIDATE_APPLICATION_MSG.no_interview_found_to_update_status,
        };
      }

      const notEndedInterviews = interviews.filter((i) => !i.isEnded);

      console.log("notEndedInterviews>>>>>>>>>>>>", notEndedInterviews);

      if (notEndedInterviews.length > 0) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.interviews_not_completed,
        };
      }

      const pendingFeedbackInterviews = interviews.filter(
        (i) => !i.isFeedbackFilled
      );

      console.log(
        "pendingFeedbackInterviews>>>>>>>>>>>>",
        pendingFeedbackInterviews
      );

      if (pendingFeedbackInterviews.length > 0) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.interview_feedback_pending,
        };
      }

      // Update the status
      // Convert string to Status enum value
      jobApplication.status = status as Status;

      console.log("jobApplication>>>>>>>>>>>>", jobApplication);

      // Save the updated job application
      const updatedJobApplication =
        await jobApplicationRepo.save(jobApplication);

      console.log("updatedJobApplication>>>>>>>>>>>>", updatedJobApplication);

      const candidate = await dataSource
        .getRepository(CandidatesModel)
        .findOne({
          where: { id: jobApplication.candidateId },
        });

      console.log("candidate>>>>>>>>>>>>", candidate);

      console.log("scheduledBy>>>>>>>>>>>>", interviews[0].scheduledBy);

      const notificationStatus =
        status === Status.FINAL_REJECT
          ? NotificationType.CANDIDATE_REJECTED
          : NotificationType.CANDIDATE_HIRED;

      const description =
        status === Status.FINAL_REJECT
          ? ` Unfortunately! We’ve decided not to proceed with ${candidate?.name}'s application at this time for ${jobApplication.job?.title} `
          : ` Hiring complete! ${candidate?.name} is officially hired for ${jobApplication.job?.title} `;

      interviews.forEach(async (interviewItem) => {
        NotificationServices.createNotification(
          orgId,
          interviewItem.scheduledBy,
          {
            type: notificationStatus,
            title: notificationStatus,
            description,
            relatedId: jobApplication.job?.id,
          }
        );
      });
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_success,
        data: updatedJobApplication,
      };
    } catch (error: unknown) {
      console.log("error>>>>>>>>>>>>", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  /**
   * Get candidate interview history
   * @param candidateId - The ID of the candidate
   * @returns A response object with the candidate's interview history
   */
  static getCandidateInterviewHistory = async (
    interviewerId: number,
    jobApplicationId: number,
    orgId: number,
    roleId: number
  ): Promise<ResponseObject> => {
    try {
      // Optimized interview data query with efficient aggregation
      // Note: Cannot JOIN users table as it's in a different database

      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const interviewHistory = await interviewRepo
        .createQueryBuilder("i")
        .select([
          "i.id AS interviewId",
          "i.round_number AS roundNumber",
          "i.interviewer_id AS interviewerId",
          "i.hard_skill_marks AS hardSkillMarks",
          "i.interview_summary AS interviewSummary",
          "i.interviewer_performance_ai_analysis AS interviewerPerformanceAiAnalysis",
          "i.interview_end_time AS endTime",
          // Use JSON_OBJECTAGG for efficient skill aggregation
          "JSON_OBJECTAGG(s.title, ise.skill_marks) AS skillScores",
        ])
        .innerJoin("jobs", "job", "job.id = i.job_id")
        .innerJoin("i.jobApplication", "jobApplication")
        .leftJoin(
          "interview_skill_evaluations",
          "ise",
          "ise.interview_id = i.id"
        )
        .leftJoin("skills", "s", "s.id = ise.skill_id")
        .where("i.jobApplicationId = :jobApplicationId", {
          jobApplicationId,
        })
        .andWhere("job.org_id = :orgId", { orgId })
        .andWhere("i.isFeedbackFilled = true")
        .andWhere("jobApplication.is_active = true")
        .groupBy(
          "i.round_number, i.interviewer_id, i.hard_skill_marks, i.interview_summary, i.interviewer_performance_ai_analysis"
        )
        .orderBy("i.round_number", "ASC")
        .getRawMany();

      console.log("interviewHistory", interviewHistory);

      // const query = dataSource
      //   .createQueryBuilder()
      //   .select([
      //     "i.id AS interviewId",
      //     "i.round_number AS roundNumber",
      //     "i.interviewer_id AS interviewerId",
      //     "i.hard_skill_marks AS hardSkillMarks",
      //     "i.interview_summary AS interviewSummary",
      //     "i.interviewer_performance_ai_analysis AS interviewerPerformanceAiAnalysis",
      //     "i.interview_end_time AS endTime",
      //     // Use JSON_OBJECTAGG for efficient skill aggregation
      //     "JSON_OBJECTAGG(s.title, ise.skill_marks) AS skillScores",
      //   ])
      //   .from("job_applications", "ja")
      //   .innerJoin("interview", "i", "i.job_application_id = ja.id")
      //   .innerJoin(
      //     "interview_skill_evaluations",
      //     "ise",
      //     "ise.interview_id = i.id"
      //   )
      //   .innerJoin("jobs", "job", "job.id = ja.job_id")
      //   .innerJoin("skills", "s", "s.id = ise.skill_id")
      //   .andWhere("job.org_id = :orgId", { orgId })
      //   .andWhere("ja.id = :jobApplicationId", { jobApplicationId })
      //   .groupBy(
      //     "i.round_number, i.interviewer_id, i.hard_skill_marks, i.interview_summary, i.interviewer_performance_ai_analysis"
      //   )
      //   .orderBy("i.round_number", "ASC");

      // Execute optimized interview data query
      // const interviewHistory = await query.getRawMany();

      // Early return if no interview history found
      if (!interviewHistory || !interviewHistory.length) {
        return {
          success: true,
          message: CANDIDATE_APPLICATION_MSG.interview_history_retrieved,
          data: [],
        };
      }

      // Extract unique interviewer IDs efficiently using Set to avoid duplicates
      const interviewerIds = [
        ...new Set(interviewHistory.map((item) => item.interviewerId)),
      ];

      // Get current user's account type to check admin privileges
      const roleRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(Role);
      const currentUser = await roleRepo.findOne({
        where: { id: roleId },
        select: ["isDefaultRole"],
      });

      console.log("currentUser", currentUser);

      const isAdmin = currentUser.isDefaultRole;

      // Optimized user lookup query - single query for all interviewer IDs
      // Using the separate database connection for users table
      const userRepo = await dbConnection.getS9DatabaseRepository(UserModel);
      const users = await userRepo.find({
        where: { id: In(interviewerIds) },
        select: ["id", "first_name", "last_name", "image"],
      });

      // Create efficient lookup map for O(1) user data access
      const userMap = new Map(
        users.map((user) => [
          user.id,
          {
            name: `${user.first_name || ""} ${user.last_name || ""}`.trim(),
            image: user.image || "",
          },
        ])
      );

      // Optimized data transformation with minimal processing
      const formattedHistory = interviewHistory.map((item) => {
        // eslint-disable-next-line prefer-destructuring
        // Parse JSON fields only once and handle null cases efficiently
        // eslint-disable-next-line prefer-destructuring
        let interviewSummary = item.interviewSummary;
        // eslint-disable-next-line prefer-destructuring
        let skillScores = item.skillScores;
        let interviewerAiAnalysis = null;

        // Efficient JSON parsing with error handling
        try {
          if (typeof interviewSummary === "string" && interviewSummary) {
            interviewSummary = JSON.parse(interviewSummary);
          } else {
            interviewSummary = { highlights: [] }; // Default to empty highlights if parsing fails
          }
        } catch (e) {
          console.warn(
            `Failed to parse interview summary for round ${item.roundNumber}:`,
            e
          );
          interviewSummary = { highlights: [] };
        }

        try {
          if (typeof skillScores === "string" && skillScores) {
            skillScores = JSON.parse(skillScores);
          }
        } catch (e) {
          console.warn(
            `Failed to parse skill scores for round ${item.roundNumber}:`,
            e
          );
          skillScores = {};
        }

        // Parse AI analysis based on admin privileges or interviewer match
        if (item.interviewerPerformanceAiAnalysis) {
          try {
            const fullAnalysis =
              typeof item.interviewerPerformanceAiAnalysis === "string"
                ? JSON.parse(item.interviewerPerformanceAiAnalysis)
                : item.interviewerPerformanceAiAnalysis;

            if (isAdmin || item.interviewerId === interviewerId) {
              // Admin or own interview: send full data including highlights
              interviewerAiAnalysis = fullAnalysis;
            } else {
              // Non-admin viewing other interviewer: send only interviewerPerformance data, exclude highlights
              interviewerAiAnalysis = { highlights: [] };
            }
          } catch (e) {
            console.warn(
              `Failed to parse AI analysis for round ${item.roundNumber}:`,
              e
            );
            interviewerAiAnalysis = { highlights: [] };
          }
        }

        // Get user information from the efficient lookup map
        const userInfo = userMap.get(item.interviewerId);

        // console.log("item", item);

        return {
          roundNumber: item.roundNumber,
          interviewerId: item.interviewerId,
          interviewerPerformanceAiAnalysis: interviewerAiAnalysis,
          interviewerName: userInfo?.name || "",
          interviewerImage: userInfo?.image || "",
          hardSkillMarks: item.hardSkillMarks,
          interviewSummary,
          skillScores: skillScores || {},
          endTime: item.endTime,
        };
      });
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.interview_history_retrieved,
        data: formattedHistory,
      };
    } catch (error) {
      Sentry.captureException(error);
      console.error("Error fetching candidate interview history:", error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_interview_history_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  /**
   * Get Application Final Summary for a candidate
   * @param candidateId - The ID of the candidate
   * @returns A response object with the candidate's skill specific assessment and final assessment data
   */

  static getApplicationFinalSummary = async (
    jobApplicationId: number
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      // get data from final_assessments table using job_application_id column
      const finalAssessmentRepo = dataSource.getRepository(
        FinalAssessmentsModel
      );

      const finalAssessmentData = await finalAssessmentRepo
        .createQueryBuilder("finalAssessment")
        .innerJoin("finalAssessment.jobApplication", "jobApplication")
        .where("finalAssessment.jobApplicationId = :jobApplicationId", {
          jobApplicationId,
        })
        .andWhere("jobApplication.isActive = true")
        .getOne();

      // Create query builder for final assessment data
      // const finalAssessmentQuery = dataSource
      //   .createQueryBuilder()
      //   .select([
      //     "ja.id AS job_application_id",
      //     "fa.development_recommendations",
      //     "fa.skill_summary",
      //     "fa.overall_success_probability",
      //   ])
      //   .from("job_applications", "ja")
      //   .leftJoin("final_assessments", "fa", "fa.job_application_id = ja.id")
      //   .where("ja.id = :jobApplicationId", { jobApplicationId });

      // const finalAssessmentData = await finalAssessmentQuery.getRawOne();

      // Transform final assessment data - handle potential null values from LEFT JOIN
      const formattedFinalAssessment = finalAssessmentData
        ? {
            jobApplicationId,
            developmentRecommendations:
              finalAssessmentData?.developmentRecommendations || null,
            skillSummary: finalAssessmentData?.skillSummary || null,
            overallSuccessProbability:
              finalAssessmentData?.overallSuccessProbability || null,
            behaviouralScores: finalAssessmentData?.behaviouralScores || null,
          }
        : {};

      const CandidateProfileSkillScoreDataResponse =
        await CandidateApplicationService.getCandidateProfileSkillScoreData(
          jobApplicationId
        );

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.application_final_summary_retrieved,
        data: {
          formattedFinalAssessment,
          candidateProfileSkillScoreData:
            CandidateProfileSkillScoreDataResponse.data,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      console.error("Error fetching skill specific assessment:", error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.application_final_summary_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  /**
   * Get candidate profile skill score data with optimized performance
   *
   * Recommended Database Indexes for optimal performance:
   * 1. CREATE INDEX idx_job_applications_candidate_id ON job_applications(candidate_id);
   * 2. CREATE INDEX idx_interview_job_application_ended ON interview(job_application_id, is_ended);
   * 3. CREATE INDEX idx_interview_skill_evaluations_interview_interviewer ON interview_skill_evaluations(interview_id, interviewer_id);
   * 4. CREATE INDEX idx_skills_id_title ON skills(id, title);
   *
   * These indexes will significantly improve query performance by:
   * - Optimizing the candidate lookup in job_applications
   * - Speeding up the interview filtering by job_application_id and is_ended status
   * - Accelerating the skill evaluations join and grouping operations
   * - Enhancing the skills table lookup performance
   */

  static getCandidateProfileSkillScoreData = async (
    jobApplicationId: number
  ): Promise<ResponseObject> => {
    // Type definitions for better type safety
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();

      // Optimized query with better field ordering and explicit casting
      const skillScoreQuery = dataSource
        .createQueryBuilder()
        .select([
          "ise.interviewer_id AS interviewer_id",
          "i.hard_skill_marks AS hard_skill_marks",
          // Optimized JSON aggregation with explicit field ordering for better performance
          "JSON_ARRAYAGG(" +
            "JSON_OBJECT(" +
            "'skill_name', s.title, " +
            "'skill_marks', ise.skill_marks, " +
            "'probability_of_success_in_this_skill', ise.probability_of_success_in_this_skill, " +
            "'strengths', ise.strengths, " +
            "'potentials_gaps', ise.potentials_gaps" +
            ")" +
            ") AS skills",
        ])
        .from("interview", "i")
        // Optimized JOIN order: most selective conditions first

        .innerJoin(
          "interview_skill_evaluations",
          "ise",
          "ise.interview_id = i.id"
        )
        .innerJoin("skills", "s", "s.id = ise.skill_id")
        .where("i.job_application_id = :jobApplicationId", { jobApplicationId })
        .innerJoin("job_applications", "ja", "ja.id = i.job_application_id")
        .andWhere("ja.isActive = true")
        .andWhere("i.isFeedbackFilled = true")
        // Optimized GROUP BY with proper ordering for index usage
        .groupBy("ise.interviewer_id, i.hard_skill_marks")
        .orderBy("ise.interviewer_id", "ASC"); // Add ordering for consistent results

      const rawSkillScores: SkillEvaluationRaw[] =
        await skillScoreQuery.getRawMany();
      // Early return with specific error message
      if (!rawSkillScores.length) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.no_skill_score_data_found,
          data: null,
        };
      }

      let totalHardSkillMarks = 0;
      // Parse JSON once and store in memory-efficient structure
      const flattenedSkillScores: FlattenedSkillScore[] = [];

      rawSkillScores.forEach((item) => {
        totalHardSkillMarks += +item.hard_skill_marks;
        console.log("item.hard_skill_marks", item.hard_skill_marks);
        console.log("item.item.skills", item.skills);

        const skills =
          typeof item.skills === "string"
            ? JSON.parse(item.skills)
            : item.skills;

        console.log("skills", skills);

        flattenedSkillScores.push(
          ...skills.map((skill) => ({
            skill_name: skill.skill_name,
            skill_marks: skill.skill_marks,
            strengths: skill.strengths,
            potentials_gaps: skill.potentials_gaps,
            probability_of_success_in_this_skill:
              skill.probability_of_success_in_this_skill,
          }))
        );
      });

      // Memory-efficient flattening with pre-calculated array size
      // const flattenedSkillScores: FlattenedSkillScore[] =
      //   groupedSkillScores.flatMap((interviewer) =>
      //     interviewer.skills.map((skill) => ({
      //       skill_name: skill.skill_name,
      //       skill_marks: skill.skill_marks,
      //       strengths: skill.strengths,
      //       potentials_gaps: skill.potentials_gaps,
      //       probability_of_success_in_this_skill:
      //         skill.probability_of_success_in_this_skill,
      //     }))
      //   );

      // Optimized career score calculation with validation
      const totalCareerSkillScore =
        rawSkillScores.length > 0
          ? Math.ceil(totalHardSkillMarks / rawSkillScores.length)
          : 0;

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.skill_specific_assessment_retrieved,
        data: {
          careerBasedSkillsScore: totalCareerSkillScore,
          skillsScores: flattenedSkillScores,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_skill_specific_assessment_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  /**
   * Generates final summary for a candidate application
   *
   * This method triggers the generation of a comprehensive final summary for a candidate
   * based on their interview performance, skill assessments, and overall evaluation data.
   * Currently returns a placeholder success response.
   *
   * @param {string} candidateId - The unique identifier of the candidate
   * @param {string} jobApplicationId - The unique identifier of the job application
   * @param {number} orgId - The organization ID for authorization
   * @returns {Promise<ResponseObject>} Promise resolving to success confirmation
   */
  static async generateFinalSummary(
    jobApplicationId: string
  ): Promise<ResponseObject> {
    try {
      // TODO: Implement actual final summary generation logic
      // This could include:
      // - Aggregating interview data from all rounds
      // - Calculating overall success probability
      // - Generating AI-powered insights and recommendations
      // - Storing the generated summary in the database

      // For now, return a placeholder success response
      const lambdaClient = new LambdaClient({
        ...clientConfig,
      });
      const command = new InvokeCommand({
        FunctionName:
          "stratum-hiring-cron-jobs-development-generateIntFinalSummary",
        Payload: JSON.stringify({
          jobApplicationId,
        }),
      });
      const lambdaResponse = await lambdaClient.send(command);
      console.log("lambdaResponse", lambdaResponse);

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.final_summary_generated_successfully,
      };
    } catch (error) {
      console.error("Error generating final summary:", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.generate_final_summary_failed,
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  static getAllHiredCandidate = async (orgId: number) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();

      const jobApplicationRepo = dataSource.getRepository(JobApplicationsModel);

      const hiredCandidate = await jobApplicationRepo
        .createQueryBuilder("jobApplication")
        .where("jobApplication.status = :status", { status: Status.HIRED })
        .innerJoin("jobApplication.candidate", "candidate")
        .andWhere("candidate.org_id = :orgId", { orgId })
        .innerJoin("jobs", "job", "job.id = jobApplication.jobId")
        .innerJoin(
          "interview",
          "interview",
          "interview.jobApplicationId = jobApplication.id"
        )
        // .innerJoin("jobApplication.interview", "interview")
        .select([
          "candidate.name as candidateName",
          "job.title as jobTitle",
          "interview.interviewer_id as interviewerId",
          "interview.roundNumber as roundNumber",
        ])
        .getRawMany();

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.get_hired_candidate_success,
        data: {
          hiredCandidate,
        },
      };
    } catch (error) {
      console.error("Error getting hired candidate:", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_hired_candidate_failed,
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  };
}

export default CandidateApplicationService;
